# Chai Pila Do - Security & Compliance Planning

## Security Framework

### Data Protection Strategy

#### Personal Data Classification
```
HIGH SENSITIVITY:
- Aadhaar numbers (hashed only)
- Bank account details (encrypted)
- Payment information (tokenized)
- Phone numbers (encrypted)

MEDIUM SENSITIVITY:
- Email addresses
- Profile information
- Transaction history
- Messages between users

LOW SENSITIVITY:
- Public profile data
- Creator usernames
- Public donation amounts
```

#### Encryption Standards
```javascript
// Data at Rest
- Database: AES-256 encryption
- File Storage: Cloudinary with encryption
- Backups: Encrypted MongoDB Atlas backups

// Data in Transit
- HTTPS/TLS 1.3 everywhere
- API communications encrypted
- Webhook signatures verified

// Sensitive Fields Encryption
const encryptSensitiveData = (data) => {
  return crypto.encrypt(data, process.env.ENCRYPTION_KEY);
};
```

### Authentication & Authorization

#### Multi-Factor Authentication
1. **Primary**: Email/Phone OTP
2. **Secondary**: Aadhaar verification via DigiLocker
3. **Optional**: SMS verification for transactions

#### Session Management
```javascript
// NextAuth.js Configuration
{
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    secret: process.env.NEXTAUTH_SECRET,
    encryption: true,
  },
  cookies: {
    secure: true, // HTTPS only
    httpOnly: true, // No client-side access
    sameSite: "strict", // CSRF protection
  }
}
```

#### Role-Based Access Control
```javascript
// User Roles
const ROLES = {
  CREATOR: 'creator',
  SUPPORTER: 'supporter', 
  ADMIN: 'admin'
};

// Permission Matrix
const PERMISSIONS = {
  [ROLES.CREATOR]: [
    'view_own_profile',
    'edit_own_profile', 
    'view_own_analytics',
    'request_withdrawal'
  ],
  [ROLES.SUPPORTER]: [
    'make_donation',
    'view_public_profiles'
  ],
  [ROLES.ADMIN]: [
    'view_all_data',
    'manage_users',
    'handle_disputes'
  ]
};
```

## Payment Security

### Razorpay Integration Security

#### PCI DSS Compliance
- **Level 1 PCI DSS**: Razorpay handles card data
- **No Card Storage**: Never store card details
- **Tokenization**: Use Razorpay tokens only
- **Secure Webhooks**: Signature verification

#### Payment Flow Security
```javascript
// 1. Create Order (Server-side)
const order = await razorpay.orders.create({
  amount: amount * 100, // Convert to paisa
  currency: 'INR',
  receipt: `receipt_${Date.now()}`,
  notes: {
    creatorId: creatorId,
    supporterId: supporterId || 'anonymous'
  }
});

// 2. Verify Payment (Server-side)
const verifyPayment = (paymentId, orderId, signature) => {
  const body = orderId + "|" + paymentId;
  const expectedSignature = crypto
    .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
    .update(body.toString())
    .digest('hex');
  
  return expectedSignature === signature;
};

// 3. Webhook Verification
const verifyWebhook = (body, signature) => {
  const expectedSignature = crypto
    .createHmac('sha256', process.env.RAZORPAY_WEBHOOK_SECRET)
    .update(JSON.stringify(body))
    .digest('hex');
    
  return expectedSignature === signature;
};
```

#### Anti-Fraud Measures
- **Rate Limiting**: Max 5 payments per minute per user
- **Amount Limits**: Max ₹10,000 per transaction
- **Velocity Checks**: Monitor unusual payment patterns
- **IP Tracking**: Log and monitor suspicious IPs

### Banking Integration Security

#### Fund Account Validation
```javascript
// Verify bank account before first withdrawal
const validateBankAccount = async (accountNumber, ifsc) => {
  try {
    const validation = await razorpay.fundAccount.validation.create({
      account_number: accountNumber,
      ifsc: ifsc,
      amount: 100, // ₹1 validation amount
      currency: 'INR',
      notes: {
        purpose: 'account_validation'
      }
    });
    return validation;
  } catch (error) {
    throw new Error('Bank account validation failed');
  }
};
```

#### Withdrawal Security
- **Minimum Amount**: ₹100
- **Daily Limits**: ₹50,000 per day
- **Manual Review**: Amounts > ₹10,000
- **Cooling Period**: 24-hour delay for first withdrawal

## Aadhaar Verification

### DigiLocker Integration

#### Secure Aadhaar Handling
```javascript
// Never store raw Aadhaar numbers
const hashAadhaar = (aadhaarNumber) => {
  const salt = process.env.AADHAAR_SALT;
  return crypto.pbkdf2Sync(aadhaarNumber, salt, 10000, 64, 'sha512').toString('hex');
};

// DigiLocker API Integration
const verifyAadhaar = async (aadhaarNumber, otp) => {
  try {
    const response = await fetch('https://api.digilocker.gov.in/public/oauth2/1/authorize', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.DIGILOCKER_ACCESS_TOKEN}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        aadhaar_number: aadhaarNumber,
        otp: otp
      })
    });
    
    return response.json();
  } catch (error) {
    throw new Error('Aadhaar verification failed');
  }
};
```

#### Privacy Protection
- **Data Minimization**: Only collect necessary Aadhaar data
- **Purpose Limitation**: Use only for verification
- **Retention Policy**: Delete after verification
- **Consent Management**: Clear consent for Aadhaar use

## API Security

### Rate Limiting
```javascript
// API Rate Limits
const rateLimits = {
  '/api/auth/login': '5 requests per minute',
  '/api/payments/create': '10 requests per minute', 
  '/api/creators/profile': '100 requests per minute',
  '/api/withdrawals/request': '3 requests per hour'
};

// Implementation with next-rate-limit
import rateLimit from 'next-rate-limit';

const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 500, // Max 500 users per minute
});

export default async function handler(req, res) {
  try {
    await limiter.check(res, 5, 'CACHE_TOKEN'); // 5 requests per minute
    // Handle request
  } catch {
    res.status(429).json({ error: 'Rate limit exceeded' });
  }
}
```

### Input Validation
```javascript
// Zod Schema Validation
import { z } from 'zod';

const donationSchema = z.object({
  amount: z.number().min(50).max(100000), // ₹50 to ₹1,00,000
  creatorId: z.string().regex(/^[0-9a-fA-F]{24}$/), // MongoDB ObjectId
  message: z.string().max(500).optional(),
  isAnonymous: z.boolean().default(false)
});

// Sanitization
import DOMPurify from 'isomorphic-dompurify';

const sanitizeInput = (input) => {
  return DOMPurify.sanitize(input, { ALLOWED_TAGS: [] });
};
```

### CORS & Security Headers
```javascript
// Next.js Security Headers
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  }
];
```

## Compliance Requirements

### Indian Regulations

#### RBI Guidelines
- **Digital Payment Security**: Follow RBI digital payment guidelines
- **KYC Compliance**: Aadhaar-based KYC for creators
- **Transaction Monitoring**: Monitor for suspicious activities
- **Reporting**: Maintain transaction records for 5 years

#### GST Compliance
```javascript
// GST Registration Required
const GST_THRESHOLD = 2000000; // ₹20 lakhs annual turnover

// Platform GST Calculation
const calculateGST = (platformFee) => {
  const gstRate = 0.18; // 18% GST
  const gstAmount = platformFee * gstRate;
  return {
    baseAmount: platformFee,
    gstAmount: gstAmount,
    totalAmount: platformFee + gstAmount
  };
};
```

#### Income Tax Compliance
- **TDS Deduction**: 1% TDS on payments > ₹10,000 per month
- **Form 26AS**: Provide TDS certificates
- **Annual Reporting**: File annual returns

### Data Protection

#### Privacy Policy Requirements
```markdown
## Data Collection
- Personal information (name, email, phone)
- Financial information (bank details, transactions)
- Usage data (analytics, preferences)

## Data Usage
- Service provision and improvement
- Payment processing
- Communication with users
- Legal compliance

## Data Sharing
- Payment processors (Razorpay)
- Government authorities (when required)
- Service providers (hosting, analytics)

## User Rights
- Access personal data
- Correct inaccurate data
- Delete account and data
- Data portability
```

#### GDPR-like Compliance
- **Consent Management**: Clear opt-in consent
- **Right to Access**: Users can download their data
- **Right to Deletion**: Complete account deletion
- **Data Portability**: Export data in standard format

## Security Monitoring

### Logging & Monitoring
```javascript
// Security Event Logging
const securityLogger = {
  loginAttempt: (userId, ip, success) => {
    console.log({
      event: 'login_attempt',
      userId,
      ip,
      success,
      timestamp: new Date().toISOString()
    });
  },
  
  paymentAttempt: (amount, creatorId, ip, success) => {
    console.log({
      event: 'payment_attempt',
      amount,
      creatorId,
      ip,
      success,
      timestamp: new Date().toISOString()
    });
  },
  
  suspiciousActivity: (userId, activity, details) => {
    console.log({
      event: 'suspicious_activity',
      userId,
      activity,
      details,
      timestamp: new Date().toISOString()
    });
  }
};
```

### Incident Response Plan
1. **Detection**: Automated monitoring alerts
2. **Assessment**: Evaluate severity and impact
3. **Containment**: Isolate affected systems
4. **Investigation**: Determine root cause
5. **Recovery**: Restore normal operations
6. **Lessons Learned**: Update security measures

## Security Testing

### Automated Security Scans
- **SAST**: Static Application Security Testing
- **DAST**: Dynamic Application Security Testing
- **Dependency Scanning**: Check for vulnerable packages
- **Container Scanning**: Secure deployment images

### Manual Security Testing
- **Penetration Testing**: Quarterly external testing
- **Code Review**: Security-focused code reviews
- **Social Engineering**: Test user awareness
- **Physical Security**: Secure development environment

## Backup & Recovery

### Data Backup Strategy
- **Database**: Daily automated backups (MongoDB Atlas)
- **Files**: Cloudinary automatic backups
- **Code**: Git repository with multiple remotes
- **Configuration**: Encrypted environment variable backups

### Disaster Recovery Plan
- **RTO**: Recovery Time Objective < 4 hours
- **RPO**: Recovery Point Objective < 1 hour
- **Backup Testing**: Monthly restore testing
- **Communication Plan**: User notification procedures
