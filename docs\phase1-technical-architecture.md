# Chai Pila Do - Technical Architecture

## System Overview

### Architecture Pattern
- **Monolithic Full-Stack**: Next.js with API routes
- **Database**: MongoDB with Mongoose ODM
- **Payment Processing**: Razorpay integration
- **Authentication**: NextAuth.js with custom providers
- **Deployment**: Vercel (serverless)

## Tech Stack Details

### Frontend
- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS + Headless UI
- **State Management**: Zustand (lightweight)
- **Forms**: React Hook Form + Zod validation
- **Icons**: Lucide React + Custom chai icons
- **Fonts**: Inter (English) + <PERSON>o <PERSON> (Hindi)

### Backend
- **Runtime**: Node.js (Vercel serverless functions)
- **API**: Next.js API routes
- **Database**: MongoDB Atlas (free tier: 512MB)
- **ODM**: Mongoose
- **Authentication**: NextAuth.js
- **File Upload**: Cloudinary (free tier: 25GB)
- **Email**: Resend (free tier: 3000 emails/month)

### Payment & Verification
- **Payment Gateway**: Razorpay
- **Aadhaar Verification**: DigiLocker API
- **SMS OTP**: Twilio or MSG91
- **Bank Verification**: Razorpay Fund Account Validation

### Hosting & Infrastructure
- **Frontend Hosting**: Vercel (free tier)
- **Database**: MongoDB Atlas (free tier)
- **CDN**: Vercel Edge Network
- **Domain**: .in domain (~₹500/year)
- **SSL**: Automatic via Vercel

## Database Schema

### Users Collection
```javascript
{
  _id: ObjectId,
  email: String (unique),
  phone: String (unique),
  aadhaarHash: String, // Hashed Aadhaar for privacy
  isVerified: Boolean,
  role: String, // 'creator' | 'supporter'
  profile: {
    name: String,
    username: String (unique),
    bio: String,
    profileImage: String,
    coverImage: String,
    socialLinks: {
      youtube: String,
      instagram: String,
      twitter: String,
      website: String
    }
  },
  bankDetails: {
    accountNumber: String (encrypted),
    ifscCode: String,
    accountHolderName: String,
    isVerified: Boolean
  },
  settings: {
    isAnonymousSupport: Boolean,
    emailNotifications: Boolean,
    smsNotifications: Boolean
  },
  createdAt: Date,
  updatedAt: Date
}
```

### Donations Collection
```javascript
{
  _id: ObjectId,
  creatorId: ObjectId (ref: Users),
  supporterId: ObjectId (ref: Users, optional),
  amount: Number, // In paisa (₹100 = 10000 paisa)
  platformFee: Number, // 2% of amount
  creatorAmount: Number, // 98% of amount
  currency: String, // 'INR'
  message: String,
  isAnonymous: Boolean,
  paymentDetails: {
    razorpayPaymentId: String,
    razorpayOrderId: String,
    paymentMethod: String, // 'upi', 'card', 'wallet'
    status: String // 'pending', 'completed', 'failed'
  },
  createdAt: Date
}
```

### Goals Collection
```javascript
{
  _id: ObjectId,
  creatorId: ObjectId (ref: Users),
  title: String,
  description: String,
  targetAmount: Number,
  currentAmount: Number,
  isActive: Boolean,
  deadline: Date,
  createdAt: Date,
  updatedAt: Date
}
```

### Withdrawals Collection
```javascript
{
  _id: ObjectId,
  creatorId: ObjectId (ref: Users),
  amount: Number,
  status: String, // 'pending', 'processing', 'completed', 'failed'
  razorpayFundAccountId: String,
  razorpayPayoutId: String,
  bankDetails: {
    accountNumber: String,
    ifscCode: String
  },
  processedAt: Date,
  createdAt: Date
}
```

## API Architecture

### Authentication Routes
- `POST /api/auth/register` - User registration
- `POST /api/auth/verify-otp` - OTP verification
- `POST /api/auth/verify-aadhaar` - Aadhaar verification
- `GET /api/auth/session` - Get current session

### Creator Routes
- `GET /api/creators/[username]` - Get creator profile
- `PUT /api/creators/profile` - Update creator profile
- `POST /api/creators/bank-details` - Add/update bank details
- `GET /api/creators/analytics` - Creator analytics
- `POST /api/creators/goals` - Create funding goal

### Payment Routes
- `POST /api/payments/create-order` - Create Razorpay order
- `POST /api/payments/verify` - Verify payment signature
- `POST /api/payments/webhook` - Razorpay webhook
- `GET /api/payments/history` - Payment history

### Withdrawal Routes
- `POST /api/withdrawals/request` - Request withdrawal
- `GET /api/withdrawals/history` - Withdrawal history
- `POST /api/withdrawals/webhook` - Payout webhook

## Security Implementation

### Data Protection
- **Encryption**: AES-256 for sensitive data
- **Hashing**: bcrypt for passwords, SHA-256 for Aadhaar
- **Environment Variables**: All secrets in .env
- **HTTPS**: Enforced everywhere
- **CORS**: Restricted to allowed origins

### Authentication Security
- **JWT**: Secure token-based auth
- **Session Management**: NextAuth.js secure sessions
- **Rate Limiting**: API route protection
- **CSRF Protection**: Built-in Next.js protection

### Payment Security
- **PCI Compliance**: Razorpay handles card data
- **Webhook Verification**: Signature validation
- **Idempotency**: Prevent duplicate payments
- **Audit Trail**: All transactions logged

## Performance Optimization

### Frontend Optimization
- **Code Splitting**: Automatic with Next.js
- **Image Optimization**: Next.js Image component
- **Lazy Loading**: React.lazy for components
- **Caching**: SWR for data fetching
- **Bundle Analysis**: @next/bundle-analyzer

### Backend Optimization
- **Database Indexing**: MongoDB compound indexes
- **Connection Pooling**: Mongoose connection management
- **Caching**: Redis for session storage (future)
- **CDN**: Cloudinary for images

### Monitoring
- **Error Tracking**: Sentry (free tier)
- **Analytics**: Vercel Analytics
- **Performance**: Web Vitals monitoring
- **Uptime**: UptimeRobot (free tier)

## Development Workflow

### Environment Setup
```bash
# Local development
npm create next-app@latest chai-pila-do --typescript --tailwind --app
cd chai-pila-do
npm install mongoose next-auth razorpay @types/node
```

### Environment Variables
```env
# Database
MONGODB_URI=mongodb+srv://...

# Authentication
NEXTAUTH_SECRET=...
NEXTAUTH_URL=http://localhost:3000

# Razorpay
RAZORPAY_KEY_ID=...
RAZORPAY_KEY_SECRET=...

# External APIs
DIGILOCKER_CLIENT_ID=...
TWILIO_ACCOUNT_SID=...
CLOUDINARY_URL=...
```

### Deployment Pipeline
1. **Development**: Local with hot reload
2. **Staging**: Vercel preview deployments
3. **Production**: Vercel production deployment
4. **Database**: MongoDB Atlas with backups

## Scalability Considerations

### Current Limits (Free Tier)
- **Vercel**: 100GB bandwidth/month
- **MongoDB**: 512MB storage
- **Cloudinary**: 25GB storage, 25GB bandwidth

### Scaling Strategy
1. **Phase 1**: Free tiers (0-1000 users)
2. **Phase 2**: Paid tiers (1000-10000 users)
3. **Phase 3**: Dedicated infrastructure (10000+ users)

### Future Optimizations
- **Database Sharding**: Horizontal scaling
- **Microservices**: Split payment service
- **CDN**: Global content delivery
- **Load Balancing**: Multiple server instances

## Security Compliance

### Data Privacy
- **GDPR Compliance**: User data rights
- **Data Retention**: Automatic cleanup
- **Consent Management**: Clear privacy policy
- **Right to Deletion**: User account deletion

### Financial Compliance
- **PCI DSS**: Payment card security
- **RBI Guidelines**: Digital payment compliance
- **GST Registration**: Tax compliance
- **AML/KYC**: Anti-money laundering

## Development Timeline

### Week 1-2: Setup & Foundation
- Project initialization
- Database schema implementation
- Basic authentication setup
- Razorpay integration

### Week 3-4: Core Features
- Creator profile system
- Donation flow implementation
- Payment processing
- Basic dashboard

### Week 5-6: Polish & Testing
- UI/UX refinement
- Security testing
- Performance optimization
- Deployment setup
